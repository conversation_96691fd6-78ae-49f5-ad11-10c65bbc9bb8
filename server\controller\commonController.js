const constants = require("../constants/constants.js");
const common = require("../constants/common.js");
const logger = require("../../misc/logger");
// Removed request-promise dependency - using axios instead
const helpers = require("../../misc/helpers");
const shopifyController = require("./shopifyController.js");
const storedenController = require("./storedenController.js");
const fbyService = require("../../services/fby_service");
const dateTime = require("node-datetime");
const bcrypt = require('bcrypt');   
const jwt = require('jsonwebtoken');
const request = require('request-promise');
const axios = require('axios');

const prestashopController = require("../../services/prestashopService/prestashop_service");

const { createProductWoocommerce } = require("./woocommerceController.js");
const { createProductMagento } = require("./magentoController.js");

exports.LoginOperations = async (req, res) => {
    const { email, password } = req.body;
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(2, 11);

    // Log incoming request
    console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] Login attempt for email: ${email}`);
    console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] Request IP: ${req.ip || req.connection.remoteAddress}`);
    console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] User-Agent: ${req.get('User-Agent')}`);

    try {
        common.getAuthUserDetails(email, null, async function (users) {
            if (users.error) {
                console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] Database error during user lookup:`, users.error);
                return res.status(500).json({ error: 'Internal Server Error' });
            } else {
                if (users.success.data.length === 0) {
                    console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] Login failed - User not found: ${email}`);
                    return res.status(401).json({ error: 'Invalid credentials' });
                }
                const user = users.success.data[0];
                console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] User found in database: ${email}, ID: ${user.id}`);

                // Compare the provided password with the hashed password in the database
                const passwordMatch = await bcrypt.compare(password, user.password);

                if (!passwordMatch) {
                    console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] Login failed - Invalid password for: ${email}`);
                    return res.status(401).json({ error: 'Invalid credentials' });
                }

                // Generate a JWT token
                const token = jwt.sign({ name: user.name, email: user.email }, process.env.JWT_KEY, { expiresIn: '1h' });
                const groupCode = user.groupCode;

                const duration = Date.now() - startTime;
                console.log(`[${new Date().toISOString()}] [LOGIN-${requestId}] Login successful for: ${email}, GroupCode: ${groupCode}, Duration: ${duration}ms`);

                res.json({ token, user: { name: user.name, email: user.email }, groupCode });
            }
        })

    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`[${new Date().toISOString()}] [LOGIN-${requestId}] Login error for: ${email}, Duration: ${duration}ms, Error:`, error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

exports.SignUpOperation = async (req, res) => {
    const {
        name,
        email,
        mobile = null,
        password,
        groupCode,
        clientId = null,
        organizationId = null,
        roleId = 6,
        createdBy = 0
    } = req.body;
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(2, 11);

    // Log incoming registration request
    console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Registration attempt for email: ${email}, name: ${name}`);
    console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Request IP: ${req.ip || req.connection.remoteAddress}`);
    console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] User-Agent: ${req.get('User-Agent')}`);
    console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] GroupCode: ${groupCode}, ClientId: ${clientId}, Mobile: ${mobile}`);
    console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] OrganizationId: ${organizationId}, RoleId: ${roleId}, CreatedBy: ${createdBy}`);

    try {
        // Check if the user already exists (check both email and mobile)
        common.getAuthUserDetails(email, mobile, async function (users) {
            if (users.error) {
                console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Database error during user lookup:`, users.error);
                return res.status(500).json({ error: 'Internal Server Error' });
            } else {
                if (users.success.data.length > 0) {
                    console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Registration failed - User already exists: ${email}`);
                    return res.status(400).json({ error: 'User already exists' });
                }

                console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] User not found, proceeding with registration for: ${email}`);

                const hashedPassword = await bcrypt.hash(password, 10);
                console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Password hashed successfully for: ${email}`);

                // Updated authUsers array to include all 9 parameters in correct order
                let authUsers = [name, email, mobile, hashedPassword, groupCode, clientId, organizationId, roleId, createdBy];
                console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Calling addAuthUser with parameters:`, {
                    name, email, mobile: mobile ? 'provided' : 'null', groupCode, clientId, organizationId, roleId, createdBy
                });

                await common.addAuthUser(authUsers, async function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Database error during user creation:`, result.error);
                        return res.status(500).json({ error: 'Internal Server Error' });
                    } else {
                        const duration = Date.now() - startTime;
                        console.log(`[${new Date().toISOString()}] [REGISTER-${requestId}] Registration successful for: ${email}, Duration: ${duration}ms`);
                        return res.json({ message: 'Registration successful' });
                    }
                });
            }
        })

    } catch (error) {
        const duration = Date.now() - startTime;
        console.error(`[${new Date().toISOString()}] [REGISTER-${requestId}] Registration error for: ${email}, Duration: ${duration}ms, Error:`, error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

exports.performCrudOperationForJobs = async (req, res) => {

    let request_type = req.query.request_type;
    try {

        if (request_type === 'get') {
            let fby_user_id = req.body.fby_user_id || '';
            let cc_operation = req.body.cc_operation || '';
            //when url hits, it insert the cron details and make status 1 as its running
            let inputs = [fby_user_id, cc_operation]
            await common.getCronLogs(inputs, function (result) {
                if (result.error) {
                    //console.log('\n ERROR: ', JSON.stringify(result.error));
                    res.send(result.error);
                } else {

                    if (!res.headersSent) {
                        res.send(result);
                    }
                }

            })
        } else if (request_type === 'create') {
            let fby_user_id = req.body.fby_user_id || '';
            let cc_operation = req.body.cc_operation || '';
            let cron_schedule = req.body.cron_schedule || '';
            let url = req.body.url || '';
            //when url hits, it insert the cron details and make status 1 as its running
            let inputs = [fby_user_id, cc_operation, cron_schedule, url]
            await common.addNewCronLogs(inputs, function (result) {
                if (result.error) {
                    //console.log('\n ERROR: ', JSON.stringify(result.error));
                    res.send(result.error);
                } else {

                    if (!res.headersSent) {
                        res.send(result);
                    }
                }
            })

        } else if (request_type === 'update') {

            let fby_user_id = req.body.fby_user_id || '';
            let cc_operation = req.body.cc_operation || '';
            let cron_schedule = req.body.cron_schedule || '';
            let url = req.body.url || '';
            //when url hits, it insert the cron details and make status 1 as its running
            let inputs = [fby_user_id, cc_operation, cron_schedule, url]
            await common.updateCronLogs(inputs, function (result) {
                if (result.error) {
                    //console.log('\n ERROR: ', JSON.stringify(result.error));
                    res.send(result.error);
                } else {

                    if (!res.headersSent) {
                        res.send(result);
                    }

                }
            })

        } else if (request_type === 'delete') {

            let fby_user_id = req.body.fby_user_id || '';
            let cc_operation = req.body.cc_operation || '';
            //when url hits, it insert the cron details and make status 1 as its running
            let inputs = [fby_user_id, cc_operation]
            await common.deleteCronLogs(inputs, function (result) {
                if (result.error) {
                    //console.log('\n ERROR: ', JSON.stringify(result.error));
                    res.send(result.error);
                } else {
                    if (!res.headersSent) {
                        res.send(result);
                    }
                }
            })
        }

    }
    catch (error) {
        //console.log(`\nfby_user_id=${fby_user_id}, ${file_and_method_name} Error: ${error.message}`);
        res.send(error.message);

    }

}

exports.getProduct = async (req, res) => {
    let cron_id = "";
    let cron_name = "";
    let isCreated = req.query.isCreated;
    try {
        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        if (req.query.fby_user_id && req.query.sku && !Boolean(isCreated)) {

                                            let productData = await common.getCreateProductBySku(client.fby_user_id, req.query.sku);
                                            res.send(productData);

                                        } else if (req.query.fby_user_id && req.query.sku && Boolean(isCreated)) {
                                            await common.getCreatedProductIDBySku(req.query.sku, async function (result) {
                                                if (result.error) {
                                                    //console.log('inputs: ', inputs);
                                                    return false;
                                                } else {
                                                    res.send(result)
                                                }
                                            })
                                        } else {
                                            await common.getCreateProductById(shopifyAccount, async function (result) {
                                                if (result.error) {
                                                    //console.log('inputs: ', inputs);
                                                    return false;
                                                } else {
                                                    res.send(result)
                                                }
                                            })
                                        }

                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }

}

exports.getAllVarient = async (req, res) => {
    let cron_id = "";
    let cron_name = "";

    try {

        let fby_id = req.query.fby_user_id;
        await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
            if (result.error) {

                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                let set_response = {};
                /* Shopify account loop start */
                for (const shopifyAccount of result.success.data) {
                    await common.getCreateProductVariantById(shopifyAccount, async function (result) {
                        if (result.error) {
                            //console.log('inputs: ', inputs);
                            return false;
                        } else {
                            res.send(result)
                        }
                    })
                }
            }
        })

    } catch (error) {
        console.log(error.message);
    }

}

exports.getChannelDetails = async (req, res) => {
    try {
        //asynchronous function for updating shopify inventory
        if (req.query.groupCode) {
            await common.getChannelDetails(req.query.groupCode, async function (result) {
                if (result.error) {
                    //console.log('inputs: ', inputs);
                    return false;
                } else {
                    res.send(result)
                }
            })
        }

    } catch (error) {
        console.log(error.message);
    }
}


exports.updateChannelStatus = async (req, res) => {
    let groupCode = req.body.groupCode;
    let channelId = req.body.channelId;
    let isActive = req.body.isActive;
    try {
        //asynchronous function for updating shopify inventory
        if (groupCode && channelId && isActive != undefined && isActive != null) {

            let inputs = [channelId, groupCode, isActive]
            await common.updateChannelStatus(inputs, async function (result) {
                if (result.error) {
                    //console.log('inputs: ', inputs);
                    return false;
                } else {
                    res.send(result)
                }
            })
        }

    } catch (error) {
        console.log(error.message);
    }
}
exports.getOrderMasterDetails = async (req, res) => {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    let cron_id = "";
    let cron_name = "";
    let order_no = req?.body?.order_no || req?.query?.order_no;
    let fby_id = req.query.fby_user_id;

    // Log API request start
    console.log(`[${new Date().toISOString()}] [${requestId}] API: get_order_master - START`);
    console.log(`[${new Date().toISOString()}] [${requestId}] Request params:`, {
        fby_user_id: fby_id,
        order_no: order_no,
        method: req.method,
        url: req.originalUrl,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
    });

    try {
        // Validate required parameters
        if (!fby_id) {
            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Missing required parameter fby_user_id`);
            return res.status(400).send({ error: "fby_user_id is required" });
        }

        console.log(`[${new Date().toISOString()}] [${requestId}] Fetching Shopify user details for fby_id: ${fby_id}`);

        //get shopify account detail
        await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Failed to get Shopify user details:`, result.error);

                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Retrieved Shopify user details. Found ${result.success.data.length} account(s)`);

                let set_response = {};
                /* Shopify account loop start */
                for (const shopifyAccount of result.success.data) {
                    console.log(`[${new Date().toISOString()}] [${requestId}] Processing Shopify account: ${shopifyAccount.domain || 'unknown'}`);

                    if (req.body.order_no) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] Fetching specific order: ${order_no}`);

                        await common.getOrderByOrderNumber(fby_id, order_no, cron_name, cron_id, async function (result) {
                            if (result.error) {
                                console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Failed to get order by number ${order_no}:`, result.error);
                                return false;
                            } else {
                                console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Retrieved order ${order_no}. Fetching order details...`);

                                await common.getOrderDetails(fby_id, order_no, cron_name, cron_id, async function (details) {
                                    if (details.error) {
                                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Failed to get order details for ${order_no}:`, details.error);
                                        return false;
                                    } else {
                                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Retrieved order details for ${order_no}. Product details count: ${details?.success?.data?.length || 0}`);

                                        result.success.data[0].productDetails = details?.success?.data || null;

                                        const endTime = Date.now();
                                        console.log(`[${new Date().toISOString()}] [${requestId}] API: get_order_master - COMPLETED successfully in ${endTime - startTime}ms`);
                                        console.log(`[${new Date().toISOString()}] [${requestId}] Response data summary:`, {
                                            orderFound: true,
                                            orderNumber: order_no,
                                            productDetailsCount: details?.success?.data?.length || 0
                                        });

                                        res.send(result)
                                    }
                                })
                            }
                        })
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] Fetching all order master details for fby_id: ${fby_id}`);

                        await common.getOrderMasterDetails(fby_id, '', '', async function (result) {
                            if (result.error) {
                                console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Failed to get order master details:`, result.error);
                                return false;
                            } else {
                                const endTime = Date.now();
                                console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Retrieved order master details. Orders count: ${result?.success?.data?.length || 0}`);
                                console.log(`[${new Date().toISOString()}] [${requestId}] API: get_order_master - COMPLETED successfully in ${endTime - startTime}ms`);
                                console.log(`[${new Date().toISOString()}] [${requestId}] Response data summary:`, {
                                    ordersCount: result?.success?.data?.length || 0,
                                    fby_user_id: fby_id
                                });

                                res.send(result)
                            }
                        })
                    }
                }
            }
        })

    } catch (error) {
        const endTime = Date.now();
        console.log(`[${new Date().toISOString()}] [${requestId}] FATAL ERROR: Unhandled exception in get_order_master:`, {
            error: error.message,
            stack: error.stack,
            fby_user_id: fby_id,
            order_no: order_no,
            duration: endTime - startTime
        });

        if (!res.headersSent) {
            res.status(500).send({ error: "Internal server error", requestId: requestId });
        }
    }

}

exports.getOrderDetails = async (req, res) => {
    let cron_id = "";
    let cron_name = "";
    let order_no = req?.body?.order_no || req?.query?.order_no;
    try {

        let fby_id = req.query.fby_user_id;
        //get shopify account detail
        await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
            if (result.error) {

                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                let set_response = {};
                /* Shopify account loop start */
                for (const shopifyAccount of result.success.data) {
                    if (order_no && req?.body.order_line_item_id) {
                        await common.getOrderDetailsByLineItem(fby_id, req.body.order_no, req.body.order_line_item_id, '', '', async function (result) {
                            if (result.error) {
                                return false;
                            } else {
                                console.log(result);
                                res.send(result)
                            }
                        })
                    } else if (req.body.order_no) {
                        await common.getOrderDetails(fby_id, order_no, '', '', async function (result) {
                            if (result.error) {

                                return false;
                            } else {
                                console.log(result);
                                res.send(result)
                            }
                        })
                    } else {
                        await common.getOrderDetailsByFbyID(fby_id, async function (result) {
                            if (result.error) {

                                return false;
                            } else {
                                console.log(result);
                                res.send(result)
                            }
                        })
                    }
                }
            }
        })

    } catch (error) {
        console.log(error.message);
    }


}
exports.getvarient = async (req, res) => {

    try {
        //asynchronous function for updating shopify inventory
        if (req.query.sku) {
            await common.getCreateProductVariantBySkuFamily(req.query.sku, async function (result) {
                if (result.error) {
                    // store get product error log
                    //console.log('inputs: ', inputs);
                    return false;
                } else {
                    res.send(result)
                }
            })
        } else {
            let item_product_id = req.query.item_product_id;
            await common.getCreatedProductVariantBySkuFamily(null, item_product_id, async function (result) {
                if (result.error) {
                    // store get product error log
                    //console.log('inputs: ', inputs);
                    return false;
                } else {
                    res.send(result)
                }
            })
        }


    } catch (error) {
        console.log(error.message);
    }


}

exports.createShopifyProductWithProductVarient = async (req, res) => {
    // for (const product of req.body.rows) {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            await createShopifyProductWithVarientFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                                .then((params) => {
                                                    console.log('params: ', params);
                                                    // if (params !== undefined) {
                                                    //   set_response[shopifyAccount.domain] = (params);
                                                    // }
                                                });
                                            common.getCreateProductVariantByDomain(shopifyAccount, "domain", cron_name, cron_id, function (result) {
                                                if (result.error) {
                                                    // store get product error log

                                                    return false;
                                                } else {
                                                    //asynchronous function for updating shopify inventory
                                                    shopifyController.createShopifyProductVariant(result.success.data, req.body, shopifyAccount, cron_name, cron_id)
                                                        .then((params) => {
                                                            if (Object.keys(params).length > 0) {
                                                                set_response[shopifyAccount.domain] = (params);
                                                            }
                                                        })
                                                }
                                            })                      //await shopifyController.pushImagesShopify(req, res);

                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                                console.log("Hello");
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}

exports.updateShopifyProductwithVarient = async (req, res) => {
    // for (const product of req.body.rows) {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            await createProductShopifyFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                                .then((params) => {
                                                    console.log('params: ', params);
                                                    // if (params !== undefined) {
                                                    //   set_response[shopifyAccount.domain] = (params);
                                                    // }
                                                });
                                            await shopifyController.updateProductsShopifyWithVarient(req, res);
                                            //await shopifyController.pushImagesShopify(req, res);

                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                                console.log("Hello");
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}

exports.updatePricing = async (req, res) => {
    // for (const product of req.body.rows) {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            await updatePricing(req.body, fby_id)
                                            await shopifyController.updateProductImageswithVarient(shopifyAccount, req.body, cron_name, cron_id);

                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                                console.log("Hello");
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}

exports.updateVarient = async (req, res) => {
    // for (const product of req.body.rows) {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            await shopifyController.updateProductImageswithVarient(shopifyAccount, req.body, cron_name, cron_id);
                                            return updateShopifyInventory(req.body, shopifyAccount, cron_name, cron_id)

                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                                console.log("Hello");
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}


const createShopifyProductWithVarientFromDigiConnector = async (product, shopifyAccount, fby_id, cron_name, cron_id) => {
    try {
        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;

        if (product !== undefined && product.length > 0) {
            for await (const item of product) {
                let img = constants.DEFAULT_SHOPIFY_IMAGE;
                if (item.images_src != undefined && item.images_src != null) {
                    img = item.images_src;
                }
                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let sku = item.sku;
                let barcode = item.barcode || '';
                let item_id = 0;
                let title = item.title;
                let item_product_id = 0;
                let inventory_item_id = 0;
                let inventory_quantity = item.quantity || 0;
                let image = item.images_src;//img;
                let imageOrder = item.image_position || 0;

                let price = item.compare_at_price || 0;
                let specialPrice = item.price || 0;
                let location_id = 0;
                let skufamily = '';
                if (item?.sku) {
                    skufamily = item?.sku;
                } else {
                    skufamily = item?.sku;
                }
                let description = item.body_html != undefined && item.body_html != null ? item.body_html : '';
                let inputs = [fby_user_id, channel, domain, owner_code, skufamily, barcode, item_id, title, item_product_id, inventory_item_id, -1, inventory_quantity, image, price, cron_name, cron_id, location_id
                    , description
                ];
                //console.log('product---   ',inputs);
                await helpers.sleep();
                await common.addCreateProduct(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
                let varient = item.variants;
                for await (const varientItem of varient) {
                    inputs = [
                        fby_user_id, channel, domain, owner_code, varientItem.sku, varientItem.barcode, item_id, title, item_product_id, inventory_item_id,
                        -1, varientItem.quantity, '', varientItem.price, cron_name, cron_id, location_id
                        , description
                    ];
                    await common.addCreateProductVariant(inputs, fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {
                            //console.log(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                        }
                    });
                }

                let insImg = [image, item.sku, item.sku, fby_id, null, imageOrder];
                //console.log('images---   ',insImg);
                await common.addImages(insImg, fby_id, cron_name, cron_id, function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(insImg));
                    }
                });

            }
        }
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
};

exports.pushShopifyProduct = async (req, res) => {
    const startTime = Date.now();
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";
    let sku = req.query.sku;
    let isUpdate = req.query.isUpdate || 'no';

    // Log API request start
    console.log(`[${new Date().toISOString()}] [${requestId}] API: push_shopify_product - START`);
    console.log(`[${new Date().toISOString()}] [${requestId}] Request params:`, {
        fby_user_id: fby_user_id,
        sku: sku,
        isUpdate: isUpdate,
        method: req.method,
        url: req.originalUrl,
        ip: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent'),
        bodySize: req.body ? JSON.stringify(req.body).length : 0,
        productCount: Array.isArray(req.body) ? req.body.length : (req.body ? 1 : 0)
    });

    try {
        // Validate required parameters
        if (!fby_user_id) {
            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Missing required parameter fby_user_id`);
            return res.status(400).send({ error: "fby_user_id is required", requestId: requestId });
        }

        if (!sku) {
            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Missing required parameter sku`);
            return res.status(400).send({ error: "sku is required", requestId: requestId });
        }

        console.log(`[${new Date().toISOString()}] [${requestId}] Fetching user details for fby_user_id: ${fby_user_id}`);

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Failed to get user details:`, result.error);

                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Retrieved user details. Found ${result.success.data.length} client(s)`);

                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    console.log(`[${new Date().toISOString()}] [${requestId}] Processing client fby_id: ${fby_id}`);

                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Failed to get Shopify user details for fby_id ${fby_id}:`, result.error);

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Retrieved Shopify user details. Found ${result.success.data.length} account(s)`);

                            let set_response = {};
                            if (result.success.data.length > 0) {
                                /* Shopify account loop start */
                                for (const shopifyAccount of result.success.data) {
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Processing Shopify account: ${shopifyAccount.domain || 'unknown'}`);
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Account details:`, {
                                        domain: shopifyAccount.domain,
                                        productPublish: shopifyAccount.productPublish,
                                        owner_code: shopifyAccount.owner_code,
                                        group_code: shopifyAccount.group_code
                                    });

                                    /**for each shopifyAccount
                                     * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                    */
                                    if (shopifyAccount.productPublish == 1) {
                                        console.log(`[${new Date().toISOString()}] [${requestId}] Product publishing enabled for domain: ${shopifyAccount.domain}`);

                                        try {
                                            //asynchronous function for updating shopify inventory
                                            try {
                                                console.log(`[${new Date().toISOString()}] [${requestId}] Creating product from DigiConnector for SKU: ${sku}`);
                                                await createProductShopifyFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id);

                                                console.log(`[${new Date().toISOString()}] [${requestId}] Creating Shopify product for SKU: ${sku}, isUpdate: ${isUpdate}`);
                                                await shopifyController.createProduct(shopifyAccount, sku, isUpdate);

                                                console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Product creation completed for SKU: ${sku} on domain: ${shopifyAccount.domain}`);

                                            } catch (error) {
                                                console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Inner product creation failed for SKU ${sku}:`, {
                                                    error: error.message,
                                                    stack: error.stack,
                                                    domain: shopifyAccount.domain
                                                });
                                            }
                                        } catch (error) {
                                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Outer product creation failed for SKU ${sku}:`, {
                                                error: error.message,
                                                stack: error.stack,
                                                domain: shopifyAccount.domain
                                            });
                                        }

                                    }
                                    else {
                                        console.log(`[${new Date().toISOString()}] [${requestId}] WARNING: Product publishing disabled for domain: ${shopifyAccount.domain}`);
                                        set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                        if (!res.headersSent) {
                                            res.send(set_response);
                                        }
                                    }

                                }
                                /* Shopify account loop end */
                                console.log(`[${new Date().toISOString()}] [${requestId}] Setting 25-second timeout for response collection`);
                                /**
                                * set time out is required to await to get all the responses from 'pushProductsShopify'
                                */
                                setTimeout(() => {
                                    const endTime = Date.now();
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Timeout reached. Sending final response after ${endTime - startTime}ms`);
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Final response:`, set_response);

                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }

                                    console.log(`[${new Date().toISOString()}] [${requestId}] API: push_shopify_product - COMPLETED via timeout in ${endTime - startTime}ms`);
                                }, 25000);
                            } else {
                                console.log(`[${new Date().toISOString()}] [${requestId}] No Shopify accounts found. Creating product from DigiConnector only`);
                                await createProductShopifyFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id);

                                const endTime = Date.now();
                                let set_response = {};
                                console.log(`[${new Date().toISOString()}] [${requestId}] API: push_shopify_product - COMPLETED (no accounts) in ${endTime - startTime}ms`);
                                res.send(set_response);
                            }
                        }
                    })
                }
            }
        });

        res.on('finish', function () {
            const endTime = Date.now();
            console.log(`[${new Date().toISOString()}] [${requestId}] Response finished. Total duration: ${endTime - startTime}ms`);
        });
    }
    catch (error) {
        const endTime = Date.now();
        console.log(`[${new Date().toISOString()}] [${requestId}] FATAL ERROR: Unhandled exception in push_shopify_product:`, {
            error: error.message,
            stack: error.stack,
            fby_user_id: fby_user_id,
            sku: sku,
            isUpdate: isUpdate,
            duration: endTime - startTime
        });

        if (!res.headersSent) {
            res.status(500).send({ error: "Internal server error", requestId: requestId });
        }
    }
}

exports.pushShopifyProductInBulk = async (req, res) => {
    // for (const product of req.body.rows) {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            await createProductShopifyFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                            await shopifyController.createShopifyProduct(req, res);
                                            //await shopifyController.pushImagesShopify(req, res);

                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                                console.log("Hello");
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}


exports.pushTrackingInBulk = async (req, res) => {
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            await createTrackingFromDigiConnector(req.body, fby_id, cron_name, cron_id)
                                            await shopifyController.pushTrackShopify(req, res);
                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                                console.log("Hello");
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}



exports.pushShopifyProductVarientInBulk = async (req, res) => {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log(`[${new Date().toISOString()}] [${requestId}] ===== pushShopifyProductVarientInBulk API STARTED =====`);

    // for (const product of req.body.rows) {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    // Log initial request details
    console.log(`[${new Date().toISOString()}] [${requestId}] Request Details:`, {
        method: req.method,
        url: req.url,
        fby_user_id: fby_user_id,
        bodyLength: req.body ? req.body.length : 0,
        headers: {
            'content-type': req.headers['content-type'],
            'authorization': req.headers.authorization ? 'Bearer [REDACTED]' : 'Not provided'
        }
    });

    // Log detailed request body for debugging
    console.log(`[${new Date().toISOString()}] [${requestId}] DETAILED REQUEST BODY:`, JSON.stringify(req.body, null, 2));

    if (!fby_user_id) {
        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Missing fby_user_id in query parameters`);
        return res.status(400).json({ error: 'fby_user_id is required' });
    }

    if (!req.body || !Array.isArray(req.body) || req.body.length === 0) {
        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Invalid or empty request body`);
        return res.status(400).json({ error: 'Request body must be a non-empty array' });
    }

    try {
        console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.userDetail for fby_user_id: ${fby_user_id}`);

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in userDetail:`, result.error);
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: userDetail returned ${result.success.data.length} clients`);

                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    console.log(`[${new Date().toISOString()}] [${requestId}] Processing client with fby_id: ${fby_id}`);

                    //get shopify account detail
                    console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.shopifyUserDetail for fby_id: ${fby_id}`);
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in shopifyUserDetail:`, result.error);
                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: shopifyUserDetail returned ${result.success.data.length} shopify accounts`);

                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                console.log(`[${new Date().toISOString()}] [${requestId}] Processing shopify account:`, {
                                    domain: shopifyAccount.domain,
                                    productPublish: shopifyAccount.productPublish,
                                    owner_code: shopifyAccount.owner_code,
                                    group_code: shopifyAccount.group_code
                                });

                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Product publishing is ENABLED for domain: ${shopifyAccount.domain}`);

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: createShopifyProductVarientFromDigiConnector`);
                                            console.log(`[${new Date().toISOString()}] [${requestId}] Parameters:`, {
                                                bodyLength: req.body ? req.body.length : 0,
                                                shopifyAccount: {
                                                    domain: shopifyAccount.domain,
                                                    owner_code: shopifyAccount.owner_code,
                                                    group_code: shopifyAccount.group_code
                                                },
                                                fby_id: fby_id
                                            });

                                            await createShopifyProductVarientFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: createShopifyProductVarientFromDigiConnector completed`);

                                            console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: shopifyController.createShopifyVarientInBulk`);
                                            await shopifyController.createShopifyVarientInBulk(req, res);
                                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: createShopifyVarientInBulk completed`);
                                            //await shopifyController.pushImagesShopify(req, res);

                                        } catch (error) {
                                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in inner try block:`, {
                                                message: error.message,
                                                stack: error.stack,
                                                code: error.code,
                                                errno: error.errno,
                                                sqlState: error.sqlState,
                                                sqlMessage: error.sqlMessage,
                                                domain: shopifyAccount.domain,
                                                errorType: error.constructor.name
                                            });

                                            // Check for specific database collation errors
                                            if (error.message && error.message.includes('Illegal mix of collations')) {
                                                console.log(`[${new Date().toISOString()}] [${requestId}] COLLATION ERROR DETECTED - This is a known database schema issue`);
                                                console.log(`[${new Date().toISOString()}] [${requestId}] Continuing with process despite collation error...`);
                                            }
                                        }
                                    } catch (error) {
                                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in outer try block:`, {
                                            message: error.message,
                                            stack: error.stack,
                                            domain: shopifyAccount.domain
                                        });
                                    }

                                }
                                else {
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Product publishing is DISABLED for domain: ${shopifyAccount.domain}`);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            console.log(`[${new Date().toISOString()}] [${requestId}] Setting 25-second timeout for response`);
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Sending final response after timeout:`, set_response);
                                    res.send(set_response);
                                } else {
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Response already sent, skipping timeout response`);
                                }
                                console.log(`[${new Date().toISOString()}] [${requestId}] Timeout completed`);
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            console.log(`[${new Date().toISOString()}] [${requestId}] Response finished event triggered`);
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        console.log(`[${new Date().toISOString()}] [${requestId}] FATAL ERROR in pushShopifyProductVarientInBulk:`, {
            message: error.message,
            stack: error.stack,
            fby_user_id: fby_user_id
        });
        if (!res.headersSent) {
            res.status(500).json({ error: 'Internal server error', requestId: requestId });
        }
    }
}


const createShopifyProductVarientFromDigiConnector = async (product, shopifyAccount, fby_id, cron_name, cron_id) => {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log(`[${new Date().toISOString()}] [${requestId}] createShopifyProductVarientFromDigiConnector - START`);

    try {
        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;

        console.log(`[${new Date().toISOString()}] [${requestId}] Processing product data:`, {
            productCount: product ? product.length : 0,
            ownerCode: ownerCode,
            groupCode: groupCode,
            domain: shopifyAccount.domain,
            fby_id: fby_id
        });

        if (product !== undefined && product.length > 0) {
            for await (const item of product) {
                console.log(`[${new Date().toISOString()}] [${requestId}] Processing item:`, {
                    sku: item.sku,
                    title: item.title,
                    price: item.price,
                    quantity: item.quantity
                });

                let img = constants.DEFAULT_SHOPIFY_IMAGE;
                if (item.images_src != undefined && item.images_src != null) {
                    img = item.images_src;
                }
                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let sku = item.sku;
                let barcode = item.barcode || '';
                let item_id = 0;
                let title = item.title;
                let item_product_id = 0;
                let inventory_item_id = 0;
                let inventory_quantity = item.quantity;
                let image = item.image_link;//img;
                let imageOrder = item.image_position || 0;
                let product_type = item.product_type || 'Test Product';
                let price = item.price || 0;
                let specialPrice = item.price || 0;
                let location_id = 0;
                let skufamiy = '';
                if (item?.skuFamily) {
                    skufamiy = item?.skuFamily;
                } else {
                    skufamiy = item?.sku;
                }
                let description = item.body_html != undefined && item.body_html != null ? item.body_html : '';

                // Convert undefined values to null for MySQL compatibility
                let inputs = [
                    fby_user_id,
                    channel,
                    domain,
                    owner_code,
                    sku,
                    barcode || null,
                    item_id,
                    title,
                    item_product_id,
                    inventory_item_id,
                    -1,
                    inventory_quantity,
                    image || null,
                    price,
                    cron_name || null,
                    cron_id || null,
                    location_id,
                    description || null
                ];

                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.addCreateProduct for SKU: ${sku}`);
                console.log(`[${new Date().toISOString()}] [${requestId}] addCreateProduct inputs (cleaned):`, inputs);
                await helpers.sleep();
                await common.addCreateProduct(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addCreateProduct for SKU ${sku}:`, result.error);
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}`, JSON.stringify(inputs));
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addCreateProduct completed for SKU: ${sku}`);
                    }
                });


                let weight_unit = item.weight_unit || '';
                let weight_value = item.weight || '';
                let dimensions_unit = item.dimensions_units || '';
                let dimensions_width = item.width || '';
                let dimensions_height = item.height || '';
                let dimensions_length = item.length || '';
                let tag = item.tags || null;
                let category = item.category || null;
                let asin = item.asin || '';
                let brand = item.brand || '';
                inputs = [fby_user_id, sku, product_type, brand, weight_unit, weight_value, dimensions_unit, dimensions_width, dimensions_height, dimensions_length, tag, category, asin];

                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.addProductUnits for SKU: ${sku}`);
                await common.addProductUnits(inputs, fby_id, async function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addProductUnits for SKU ${sku}:`, result.error);
                        logger.logError(`getProductType fby_user_id: ${fby_id}, sku${sku}`, JSON.stringify(inputs));
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addProductUnits completed for SKU: ${sku}`);
                    }
                });

                let insImg = [image || null, sku, skufamiy, fby_user_id, null, 0];
                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.addImages for SKU: ${sku}`);
                await common.addImages(insImg, fby_user_id, cron_name, cron_id, function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addImages for SKU ${sku}:`, result.error);
                        logger.logError(`getProductsFby fby_user_id: ${fby_user_id}, sku${sku}`, JSON.stringify(insImg));
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addImages completed for SKU: ${sku}`);
                    }
                });

                inputs = [
                    fby_user_id,
                    channel,
                    domain,
                    owner_code,
                    sku,
                    barcode || null,
                    item_id,
                    title,
                    item_product_id,
                    inventory_quantity,
                    -1,
                    inventory_quantity,
                    image || null,
                    price,
                    cron_name || null,
                    cron_id || null,
                    location_id,
                    description || null
                ];
                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.addCreateProductVariant for SKU: ${sku}`);
                await common.addCreateProductVariant(inputs, fby_user_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addCreateProductVariant for SKU ${sku}:`, result.error);
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addCreateProductVariant completed for SKU: ${sku}`);
                    }
                });

                inputs = [fby_user_id, sku, sku];
                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.addSkuFamily for SKU: ${sku}`);
                await common.addSkuFamily(inputs, fby_user_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addSkuFamily for SKU ${sku}:`, result.error);
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addSkuFamily completed for SKU: ${sku}`);
                    }
                });

                inputs = [fby_user_id, sku, item.option_1_name || null, item.option_1_value || null, item.option_2_name || null, item.option_2_value || null];
                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.addProductOptions for SKU: ${sku}`);
                console.log(`[${new Date().toISOString()}] [${requestId}] addProductOptions inputs:`, {
                    fby_user_id: fby_user_id,
                    sku: sku,
                    option_1_name: item.option_1_name || null,
                    option_1_value: item.option_1_value || null,
                    option_2_name: item.option_2_name || null,
                    option_2_value: item.option_2_value || null
                });

                try {
                    await common.addProductOptions(inputs, fby_id, async function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addProductOptions for SKU ${sku}:`, {
                                error: result.error,
                                inputs: inputs,
                                errorType: typeof result.error === 'object' ? result.error.constructor.name : 'Unknown'
                            });

                            // Check for collation error specifically
                            if (result.error && result.error.data && result.error.data.includes && result.error.data.includes('Illegal mix of collations')) {
                                console.log(`[${new Date().toISOString()}] [${requestId}] COLLATION ERROR in addProductOptions - This is a known database schema issue for SKU: ${sku}`);
                            }

                            logger.logError(`getProductType fby_user_id: ${fby_id}, sku: ${sku}`, JSON.stringify(inputs));
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addProductOptions completed for SKU: ${sku}`);
                        }
                    });
                } catch (optionsError) {
                    console.log(`[${new Date().toISOString()}] [${requestId}] EXCEPTION in addProductOptions for SKU ${sku}:`, {
                        error: optionsError.message,
                        stack: optionsError.stack,
                        code: optionsError.code,
                        errno: optionsError.errno,
                        sqlState: optionsError.sqlState,
                        inputs: inputs
                    });
                }

            }
        } else {
            console.log(`[${new Date().toISOString()}] [${requestId}] WARNING: Product data is undefined or empty`);
        }

        console.log(`[${new Date().toISOString()}] [${requestId}] createShopifyProductVarientFromDigiConnector - COMPLETED SUCCESSFULLY`);
    }
    catch (error) {
        console.log(`[${new Date().toISOString()}] [${requestId}] FATAL ERROR in createShopifyProductVarientFromDigiConnector:`, {
            message: error.message,
            stack: error.stack,
            fby_id: fby_id,
            shopifyAccount: shopifyAccount?.domain || 'unknown'
        });
    }
};


const createShopifyInventaryFromDigiConnector = async (product, shopifyAccount, fby_id, cron_name, cron_id) => {
    try {
        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;

        if (product !== undefined && product.length > 0) {
            for await (const item of product) {

                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let inventory_quantity = item.inventory_quantity;
                let sku = item?.sku || '';

                //console.log('product---   ',inputs);
                await common.getCreateProductVariantBySku(sku, async function (result) {
                    if (result.error) {

                    } else {
                        let product = result.success.data[0];
                        let inputs = [
                            fby_user_id, channel, domain, owner_code, sku, product.barcode, product.item_id, product.title, product.item_product_id, inventory_quantity,
                            -1, inventory_quantity, product.image, product.price, product.cron_name, product.cron_id, product.location_id
                            , product.description
                        ];
                        await common.addCreateProductVariant(inputs, fby_user_id, cron_name, cron_id, async function (result) {
                            if (result.error) {
                                //console.log(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                            }
                        });
                    }
                });

            }
        }
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
};


const createProductShopifyFromDigiConnector = async (productData, shopifyAccount, fby_id, cron_name, cron_id) => {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log(`[${new Date().toISOString()}] [${requestId}] createProductShopifyFromDigiConnector - START`);

    try {
        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;

        console.log(`[${new Date().toISOString()}] [${requestId}] Processing productData with ${productData?.length || 0} items`);

        if (productData !== undefined && productData.length > 0) {
            for await (const item of productData) {
                const productFields = item.newProduct.fields;
                console.log(`[${new Date().toISOString()}] [${requestId}] Processing product with SKU: ${productFields?.sku || 'unknown'}`);

                // Extract relevant product fields
                const {
                    title,
                    tags,
                    brand,
                    weight,
                    dimensions,
                    short_description,
                    categories,
                    price,
                    quantity,
                    sku,
                    barcode,
                    asin,
                } = productFields;

                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let item_id = 0;
                let item_product_id = 0;
                let inventory_item_id = 0;
                let inventory_quantity = parseInt(quantity) || 0;

                // Use dynamic image from request body, fallback to photos array, then default
                // Check multiple possible image field names from the request
                let image = item.image_link || item.image || productFields.image || productFields.image_link || item.newProduct.photos[0] || constants.DEFAULT_SHOPIFY_IMAGE
                console.log(`[${new Date().toISOString()}] [${requestId}] Image selection for SKU ${sku}:`, {
                    item_image_link: item.image_link,
                    item_image: item.image,
                    productFields_image: productFields.image,
                    productFields_image_link: productFields.image_link,
                    photos_first: item.newProduct.photos[0],
                    selected_image: image
                });
                let location_id = 0;
                let description = short_description || productFields.description;
                let inputs = [fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id, -1, inventory_quantity, image, price, cron_name, cron_id, location_id
                    , description
                ];
                await helpers.sleep();

                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING DB: addCreateProduct for SKU: ${sku}`);
                console.log(`[${new Date().toISOString()}] [${requestId}] addCreateProduct inputs:`, {
                    fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title,
                    item_product_id, inventory_item_id, inventory_quantity, image, price,
                    cron_name, cron_id, location_id, description
                });

                await common.addCreateProduct(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addCreateProduct for SKU ${sku}:`, result.error);
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}`, JSON.stringify(inputs));
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addCreateProduct completed for SKU: ${sku}`);
                    }
                });

                let product_type = item.product_type || 'Test Product';
                let weight_unit = weight.unit || '';
                let weight_value = weight.value || '';
                let dimensions_unit = dimensions.unit || '';
                let dimensions_width = dimensions.width || '';
                let dimensions_height = dimensions.height || '';
                let dimensions_length = dimensions.length || '';
                let tag = tags && tags[0] ? tags[0] : null;
                let category = categories && categories[0] ? categories[0] : null;
                inputs = [fby_user_id, sku, product_type, brand, weight_unit, weight_value, dimensions_unit, dimensions_width, dimensions_height, dimensions_length, tag, category, asin || ''];

                console.log(`[${new Date().toISOString()}] [${requestId}] CALLING DB: addProductUnits for SKU: ${sku}`);
                console.log(`[${new Date().toISOString()}] [${requestId}] addProductUnits inputs:`, {
                    fby_user_id, sku, product_type, brand, weight_unit, weight_value,
                    dimensions_unit, dimensions_width, dimensions_height, dimensions_length,
                    tag, category, asin: asin || ''
                });

                await common.addProductUnits(inputs, fby_id, async function (result) {
                    if (result.error) {
                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addProductUnits for SKU ${sku}:`, result.error);
                        logger.logError(`getProductType fby_user_id: ${fby_id}, sku${sku}`, JSON.stringify(inputs));
                    } else {
                        console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addProductUnits completed for SKU: ${sku}`);
                    }
                });

                console.log(`[${new Date().toISOString()}] [${requestId}] Processing ${item.newProduct.photos?.length || 0} photos for SKU: ${sku}`);
                for await (const photo of item.newProduct.photos) {
                    let insImg = [photo || '', sku, sku, fby_user_id, null, 0];

                    console.log(`[${new Date().toISOString()}] [${requestId}] CALLING DB: addImages for SKU: ${sku}`);
                    console.log(`[${new Date().toISOString()}] [${requestId}] addImages inputs:`, {
                        photo: photo || '', sku, fby_user_id, imageOrder: 0
                    });

                    await common.addImages(insImg, fby_user_id, cron_name, cron_id, function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addImages for SKU ${sku}:`, result.error);
                            logger.logError(`getProductsFby fby_user_id: ${fby_user_id}, sku${sku}`, JSON.stringify(insImg));
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addImages completed for SKU: ${sku}`);
                        }
                    });
                }
                let skuFamily = sku;

                // Handle options safely
                const option1 = item.newProduct.options && item.newProduct.options[0] ? item.newProduct.options[0] : { name: 'Size', values: ['Default'] };
                const option2 = item.newProduct.options && item.newProduct.options[1] ? item.newProduct.options[1] : { name: 'Color', values: ['Default'] };
                let optionCombination = []

                if (option1.values && option2.values) {
                    option1.values.forEach(option1Value => {
                        option2.values.forEach(option2Value => {
                            optionCombination.push([
                                option1.name || 'Option1',
                                option1Value || 'Default',
                                option2.name || 'Option2',
                                option2Value || 'Default'
                            ])
                        });
                    });
                } else {
                    // Default option combination if no options provided
                    optionCombination.push(['Size', 'Default', 'Color', 'Default']);
                }
                console.log(`[${new Date().toISOString()}] [${requestId}] Processing ${item.newProduct.variants?.length || 0} variants for SKU: ${sku}`);
                let count = 0;
                for await (const variant of item.newProduct.variants) {
                    const { price, sku: variantSku, quantity, barcode, title } = variant;
                    console.log(`[${new Date().toISOString()}] [${requestId}] Processing variant ${count + 1} with SKU: ${variantSku}`);

                    inputs = [
                        fby_user_id, channel, domain, owner_code, variantSku, barcode, item_id, title, item_product_id, inventory_quantity,
                        -1, quantity, image, price, cron_name, cron_id, location_id
                        , description
                    ];

                    console.log(`[${new Date().toISOString()}] [${requestId}] CALLING DB: addCreateProductVariant for variant SKU: ${variantSku}`);
                    console.log(`[${new Date().toISOString()}] [${requestId}] addCreateProductVariant inputs:`, {
                        fby_user_id, channel, domain, owner_code, sku: variantSku, barcode,
                        item_id, title, item_product_id, inventory_quantity, quantity,
                        image, price, cron_name, cron_id, location_id, description
                    });

                    await common.addCreateProductVariant(inputs, fby_user_id, cron_name, cron_id, async function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addCreateProductVariant for variant SKU ${variantSku}:`, result.error);
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addCreateProductVariant completed for variant SKU: ${variantSku}`);
                        }
                    });

                    inputs = [fby_user_id, variantSku, skuFamily];
                    console.log(`[${new Date().toISOString()}] [${requestId}] CALLING DB: addSkuFamily for variant SKU: ${variantSku}`);
                    console.log(`[${new Date().toISOString()}] [${requestId}] addSkuFamily inputs:`, {
                        fby_user_id, sku: variantSku, skuFamily
                    });

                    await common.addSkuFamily(inputs, fby_user_id, cron_name, cron_id, async function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addSkuFamily for variant SKU ${variantSku}:`, result.error);
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addSkuFamily completed for variant SKU: ${variantSku}`);
                        }
                    });

                    // Safely handle option combination
                    const currentOptionCombination = optionCombination[count] || ['Size', 'Default', 'Color', 'Default'];
                    inputs = [fby_user_id, variantSku].concat(currentOptionCombination);
                    console.log(`[${new Date().toISOString()}] [${requestId}] CALLING DB: addProductOptions for variant SKU: ${variantSku}`);
                    console.log(`[${new Date().toISOString()}] [${requestId}] addProductOptions inputs:`, {
                        fby_user_id, sku: variantSku, optionCombination: currentOptionCombination
                    });

                    count++;
                    common.addProductOptions(inputs, fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in addProductOptions for variant SKU ${variantSku}:`, result.error);
                            logger.logError(`getProductType fby_user_id: ${fby_id}, sku: ${variantSku}`, JSON.stringify(inputs));
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: addProductOptions completed for variant SKU: ${variantSku}`);
                        }
                    });
                }
            }
        }
        console.log(`[${new Date().toISOString()}] [${requestId}] createProductShopifyFromDigiConnector - COMPLETED SUCCESSFULLY`);
    }
    catch (error) {
        console.log(`[${new Date().toISOString()}] [${requestId}] FATAL ERROR in createProductShopifyFromDigiConnector:`, {
            error: error.message,
            stack: error.stack,
            fby_id: fby_id,
            shopifyAccount: shopifyAccount?.domain || 'unknown'
        });
    }
};


const createTrackingFromDigiConnector = async (items, fby_id, cron_name, cron_id) => {
    try {

        for (let item of items) {
            try {

                let traking = "";
                let shipmentDate = "";
                let carrier = "";
                let ship_url = "";
                let isReturn = "";
                //console.log(`\n${counter} ) getTrackList, fby_user_id:${fby_id}, url: ${constants.FBY_NOTIFY_ORDER_URL}, params: ${paramsJson}\nOrder To Notify : \n`, JSON.stringify(item));


                traking = item.tracking;
                shipmentDate = item.shipmentDate;
                carrier = item.carrier;
                ship_url = item.url;
                isReturn = item.isReturn;

                let updt_time = dateTime.create();

                try {
                    let order_no = item.channelOrderId;
                    let sku = item.skuCode != undefined ? item.skuCode : "";
                    let logMessage = `fby_user_id: ${fby_id}, ${cron_name}`;
                    if (order_no != '') {
                        logMessage = `${logMessage}, order_no: ${order_no}`

                    }
                    if (sku != '') {
                        logMessage = `${logMessage}, sku: ${sku}`

                    }


                    //   await logger.LogForAlert(
                    //     fby_id,
                    //     order_no,
                    //     sku,
                    //     logMessage,
                    //     item,
                    //     constants.LOG_LEVEL.INFO,
                    //     fby_alert_code,
                    //     cron_name,
                    //     cron_id
                    //   );
                } catch (error) {
                    console.log(error);

                }

                let item_arr = [
                    cron_name,
                    cron_id,
                    updt_time.format('Y-m-d H:M:S'),
                    traking,
                    carrier,
                    ship_url,
                    item.channelOrderId,
                    item.channelCode,
                    item.skuEan,
                    item.skuCode != undefined ? item.skuCode : ""
                ];


                // update order Track Number

                await common.updateOrder(item_arr, fby_id, cron_name, cron_id, function (result) {
                    if (result.error) {
                        //console.log(`\n${counter} ) getTrackList, fby_user_id:${fby_id}, url: ${constants.FBY_NOTIFY_ORDER_URL}, params: ${paramsJson}\nOrder To Notify : \n`, JSON.stringify(item));
                        //console.log('\n ERROR: ', JSON.stringify(result.error));
                    }
                })

                let order = {
                    fby_user_id: fby_id,
                    order_no: item.channelOrderId,
                    sku: item.skuCode,
                    barcode: item.skuEan,
                    order_line_item_id: null,
                    original_Channel_OrderId: item.originalChannelOrderId
                }
                //if (order.order_no == '5588716093784' || order.order_no ==5590845161816) {
                await fbyService.changeOrderStatus(null, order, cron_id, cron_name, cron_id);
                //}
            }
            catch (error) {
                //console.log(`\nERROR:${error.message}}\nWHILE ${counter} )getTrackList, fby_user_id:${fby_id}, url: ${constants.FBY_NOTIFY_ORDER_URL}, params: ${paramsJson}\nOrder To Notify : \n`, JSON.stringify(item));

                //console.log('\n ERROR: ',);
            }
        }
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
};


const updatePricing = async (product, fby_id) => {
    try {

        if (product !== undefined && product.length > 0) {
            for await (const item of product) {

                let fby_user_id = fby_id;
                let sku = item.sku;
                let price = item.price || 0;
                let specialPrice = item.price || 0;

                await helpers.sleep();
                let inputs = [fby_user_id, price, specialPrice, sku];
                await common.updatePrices(inputs, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductPricesFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });

            }
        }
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
}


exports.updateShopifyInventoryInBulk = async (req, res) => {
    // for (const product of req.body.rows) {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            await createShopifyInventaryFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                            await updateShopifyInventory(req.body, shopifyAccount, cron_name, cron_id);

                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                    if (!res.headersSent) {
                                        res.send(set_response);
                                    }
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                                console.log("Hello");
                            }, 25000);
                        }
                    })
                }
            }
        });
        res.on('finish', function () {
            // let dt = dateTime.create();
            // let inputs = [dt.format('Y-m-d H:M:S'), cron_id]
            // common.updateCron(inputs, cron_name, cron_id, function (result) {
            //   if (result.error) {
            //     mail.cronProcessErrMail(cron_name, cron_id, req.query.fby_user_id, JSON.stringify(result.error));
            //   }
            // })
        });
    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}

const updateShopifyInventory = async function (productDetails, client, cron_name, cron_id) {
    let fby_user_id = client.fby_user_id;
    const set_response = {};
    try {
        productDetails.forEach(async (item) => {
            await common.getCreatedProductVarientIDBySku(item.sku, async function (product) {
                if (product.error) {

                }
                else {

                    product = product.success.data[0];
                    let url = `${client.domain}admin/api/2023-04/inventory_levels/set.json`;
                    let locationId = product.location_id;
                    let options = {
                        method: "post",
                        uri: url,
                        headers: {
                            "Content-Type": "application/json",
                            'X-Shopify-Access-Token': `${client.token}`
                        },
                        body:
                            { "location_id": locationId, "inventory_item_id": product.inventory_item_id, "available": item.inventory_quantity || item.quantity },

                        json: true,
                    };

                    await request(options)
                        .then(async (parsedBody) => {
                            console.log('post image res parsedBody: ', parsedBody);
                            //insert products got from shopify into products table

                            await common.getCreatedProductVarientIDBySku(item.sku, async function (product) {
                                if (product.error) {

                                } else {
                                    let prod = product.success.data[0];
                                    let inputs = [prod.fby_user_id, prod.channel, prod.domain, prod.owner_code, prod.sku, prod.barcode, prod.item_id, prod.title, prod.item_product_id, prod.inventory_item_id, prod.inventory_quantity, parsedBody.inventory_level.available, prod.image, prod.price, prod.cron_name, prod.cron_id, prod.location_id
                                        , prod.description
                                    ];
                                    await common.addCreatedProductVariant(inputs, fby_user_id, cron_name, cron_id, function (result) {
                                        if (result.error) {
                                            logger.logError(`createProductVariants fby_user_id: ${client.fby_user_id}, sku${sku}, ${url}`, JSON.stringify(inputs));

                                        }
                                    })
                                }
                            });
                        })
                        .catch(function (err) {

                            console.log(err);
                            //console.log('\n ERROR: ', JSON.stringify(inputs));
                        });


                }
            });

        })         // //console.log('imageOrder: ', imageOrder);

        return set_response;
    }
    catch (error) {
        console.log('\n ERROR: ', error.message);
    }

}


exports.deleteShopifyProduct = async (req, res) => {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        common.userDetail(req.query.fby_user_id, cron_name, cron_id, function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                await common.getCreatedProductIDBySku(req.query.sku, async function (product) {
                                    if (product.error) {

                                    } else {
                                        if (product.success.data.length > 0) {
                                            await deleteShopifyProduct(product.success.data[0], shopifyAccount, cron_name, cron_id)
                                                .then((params) => {
                                                    set_response[req.query.sku] = "Successfully deleted sku";
                                                    res.send(set_response);
                                                    console.log();
                                                })
                                        }

                                    }
                                })

                                await common.getCreatedProductVarientIDBySku(req.body.sku, async function (variant) {
                                    if (variant.error) {

                                    } else {
                                        if (variant.success.data.length > 0) {
                                            await deleteShopifyProductVariant(variant.success.data[0], shopifyAccount, cron_name, cron_id)
                                                .then((params) => {
                                                    set_response[req.body.sku] = "Successfully deleted sku";
                                                    res.send(set_response);
                                                    console.log();
                                                })
                                        }
                                    }
                                })

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 15000);
                        }
                    })
                }
            }
        });

    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}

exports.pushWoocommerceProduct = async (req, res) => {
    // for (const product of req.body.rows) {
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        common.userDetail(req.query.fby_user_id, cron_name, cron_id, function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            createProductWoocommerceFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                                .then((params) => {
                                                    // if (Object.keys(params).length > 0) {
                                                    //   set_response[shopifyAccount.domain] = (params);
                                                    // }
                                                })
                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                        await common.getCreateProductByDomain(shopifyAccount, "domain", cron_name, cron_id, async function (result) {
                                            if (result.error) {
                                                // store get product error log
                                                return false;
                                            } else {
                                                //asynchronous function for updating shopify inventory
                                                await createProductWoocommerce(result.success.data, shopifyAccount, cron_name, cron_id)




                                            }
                                        })
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 15000);
                        }
                    })
                }
            }
        });

    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}


const deleteShopifyProduct = async (product, client) => {
    const url = `${client.domain}/admin/api/2023-07/products/${product.item_product_id}.json`;
    const options = {
        method: "delete",
        uri: url,
        headers: {
            "Content-Type": "application/json",
            'X-Shopify-Access-Token': `${client.token}`
        },
        json: true,
    };
    await request(options)
        .then(async (parsedBody) => {
            await common.deleteProducts(
                product.sku,
                client.fby_user_id,
                function (result) {
                    //do nothing
                    logger.logError(`Product Deleted`, JSON.stringify(result));
                });

            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nResult: \n${JSON.stringify(parsedBody)}`);
        })
        .catch((err) => {
            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nERROR: ${err.message}`);
            console.log("delete failed", + err.message);
        });
}


const deleteShopifyProductVariant = async (variant, client) => {
    const url = `${client.domain}admin/api/2023-04/products/${variant.item_product_id}/variants/${variant.item_id}.json`
    const options = {
        method: "delete",
        uri: url,
        headers: {
            "Content-Type": "application/json",
            'X-Shopify-Access-Token': `${client.token}`
        },
        json: true,
    };
    await request(options)
        .then(async (parsedBody) => {
            await common.deleteProducts(
                variant.sku,
                client.fby_user_id,
                function (result) {
                    //do nothing
                    logger.logError(`Product Deleted`, JSON.stringify(result));
                });

            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nResult: \n${JSON.stringify(parsedBody)}`);
        })
        .catch((err) => {
            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nERROR: ${err.message}`);
            console.log("delete failed", + err.message);
        });
}

const createProductWoocommerceFromDigiConnector = async (product, shopifyAccount, fby_id, cron_name, cron_id, callback) => {
    try {

        let set_response = {};
        let page = 1;
        let total_page = 0;
        let count = 1;

        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;
        let item_per_page = constants.FBY_PERPAGE;
        let items = [];

        if (product !== undefined && product.length > 0) {
            product.forEach(async (item) => {
                let img = constants.DEFAULT_SHOPIFY_IMAGE;
                if (item.images_src != undefined && item.images_src != null) {
                    img = item.images_src;
                }
                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let sku = item.sku;
                let barcode = item.barcode;
                let item_id = 0;
                let title = item.name;
                let item_product_id = 0;
                let inventory_item_id = 0;
                let inventory_quantity = item.quantity;
                let image = item.images_src;//img;
                let imageOrder = item.image_position;

                let price = item.price;
                let specialPrice = item.specialPrice;
                let location_id = 0;
                let description = item.description != undefined && item.description != null ? item.description : '';
                let inputs = [fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id, -1, inventory_quantity, image, specialPrice, cron_name, cron_id, location_id
                    , description
                ];
                //console.log('product---   ',inputs);
                await common.addCreateProduct(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });

                inputs = [
                    fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id,
                    -1, inventory_quantity, '', price, cron_name, cron_id, location_id
                    , description
                ];
                await common.addCreateProductVariant(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        //console.log(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
                let insImg = [image, item.sku, item.sku, fby_id, null, imageOrder];
                //console.log('images---   ',insImg);
                await common.addImages(insImg, fby_id, cron_name, cron_id, function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(insImg));
                    }
                });
                inputs = [fby_user_id, price, specialPrice, sku];
                await common.updatePrices(inputs, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductPricesFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
            });
        }

        return set_response;
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
};

exports.deleteWocommerceProduct = async (req, res) => {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        common.userDetail(req.query.fby_user_id, cron_name, cron_id, function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                await common.getCreatedProductIDBySku(req.body.sku, async function (product) {
                                    if (product.error) {

                                    } else {
                                        await deleteWocommerceProduct(product.success.data[0], shopifyAccount, cron_name, cron_id)
                                            .then((params) => {
                                                // if (Object.keys(params).length > 0) {
                                                //   set_response[shopifyAccount.domain] = (params);
                                                // }
                                                console.log();
                                            })
                                    }
                                })

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 15000);
                        }
                    })
                }
            }
        });

    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}

const deleteWocommerceProduct = async (product, client) => {
    const url = `${client.domain}/wp-json/wc/v3/products/${product.item_product_id}`;
    const options = {
        method: "delete",
        uri: url,
        headers: {
            "Content-Type": "application/json",
            'X-Shopify-Access-Token': `${client.token}`
        },
        json: true,
    };
    await request(options)
        .then(async (parsedBody) => {
            common.deleteProducts(
                product.sku,
                client.fby_user_id,
                function (result) {
                    //do nothing
                    logger.logError(`Product Deleted`, JSON.stringify(result));
                });

            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nResult: \n${JSON.stringify(parsedBody)}`);
        })
        .catch((err) => {
            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nERROR: ${err.message}`);
        });
}


exports.pushMagentoProduct = async (req, res) => {
    // for (const product of req.body.rows) {
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        common.userDetail(req.query.fby_user_id, cron_name, cron_id, function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            createProductMagentoFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                                .then((params) => {
                                                    // if (Object.keys(params).length > 0) {
                                                    //   set_response[shopifyAccount.domain] = (params);
                                                    // }
                                                })
                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                        await common.getCreateProductByDomain(shopifyAccount, "domain", cron_name, cron_id, async function (result) {
                                            if (result.error) {
                                                // store get product error log
                                                return false;
                                            } else {
                                                //asynchronous function for updating shopify inventory
                                                await createProductMagento(result.success.data, shopifyAccount, cron_name, cron_id)
                                                    .then((params) => {
                                                        // if (Object.keys(params).length > 0) {
                                                        //   set_response[shopifyAccount.domain] = (params);
                                                        // }
                                                    })

                                            }
                                        })
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 15000);
                        }
                    })
                }
            }
        });

    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}


const createProductMagentoFromDigiConnector = async (product, shopifyAccount, fby_id, cron_name, cron_id, callback) => {
    try {

        let set_response = {};
        let page = 1;
        let total_page = 0;
        let count = 1;

        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;
        let item_per_page = constants.FBY_PERPAGE;
        let items = [];

        if (product !== undefined && product.length > 0) {
            product.forEach(async (item) => {
                let img = constants.DEFAULT_SHOPIFY_IMAGE;
                if (item.images_src != undefined && item.images_src != null) {
                    img = item.images_src;
                }
                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let sku = item.sku;
                let barcode = item.barcode;
                let item_id = 0;
                let title = item.name;
                let item_product_id = 0;
                let inventory_item_id = 0;
                let inventory_quantity = item.quantity;
                let image = item.images_src;//img;
                let imageOrder = item.image_position;

                let price = item.price;
                let specialPrice = item.specialPrice;
                let location_id = 0;
                let description = item.description != undefined && item.description != null ? item.description : '';
                let inputs = [fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id, -1, inventory_quantity, image, specialPrice, cron_name, cron_id, location_id
                    , description
                ];
                //console.log('product---   ',inputs);
                await common.addCreateProduct(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });

                inputs = [
                    fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id,
                    -1, inventory_quantity, '', price, cron_name, cron_id, location_id
                    , description
                ];
                await common.addCreateProductVariant(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        //console.log(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
                let insImg = [image, item.sku, item.sku, fby_id, null, imageOrder];
                //console.log('images---   ',insImg);
                await common.addImages(insImg, fby_id, cron_name, cron_id, function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(insImg));
                    }
                });
                inputs = [fby_user_id, price, sku];
                await common.updatePrices(inputs, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductPricesFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
            });
        }

        return set_response;
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
};



exports.pushPrestashopProduct = async (req, res) => {
    // for (const product of req.body.rows) {
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        common.userDetail(req.query.fby_user_id, cron_name, cron_id, function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            createProductPrestashopFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                                .then((params) => {
                                                    // if (Object.keys(params).length > 0) {
                                                    //   set_response[shopifyAccount.domain] = (params);
                                                    // }
                                                })
                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                        await common.getCreateProductByDomain(shopifyAccount, "domain", cron_name, cron_id, async function (result) {
                                            if (result.error) {
                                                // store get product error log
                                                return false;
                                            } else {
                                                //asynchronous function for updating shopify inventory
                                                await prestashopController.createProductPrestashop(result.success.data, shopifyAccount, cron_name, cron_id)
                                                    .then((params) => {
                                                        // if (Object.keys(params).length > 0) {
                                                        //   set_response[shopifyAccount.domain] = (params);
                                                        // }
                                                    })

                                            }
                                        })
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 15000);
                        }
                    })
                }
            }
        });

    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}



const createProductPrestashopFromDigiConnector = async (product, shopifyAccount, fby_id, cron_name, cron_id, callback) => {
    try {

        let set_response = {};
        let page = 1;
        let total_page = 0;
        let count = 1;

        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;
        let item_per_page = constants.FBY_PERPAGE;
        let items = [];

        if (product !== undefined && product.length > 0) {
            product.forEach(async (item) => {
                let img = constants.DEFAULT_SHOPIFY_IMAGE;
                if (item.images_src != undefined && item.images_src != null) {
                    img = item.images_src;
                }
                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let sku = item.sku;
                let barcode = item.barcode;
                let item_id = 0;
                let title = item.name;
                let item_product_id = 0;
                let inventory_item_id = 0;
                let inventory_quantity = item.quantity;
                let image = item.images_src;//img;
                let imageOrder = item.image_position;

                let price = item.price;
                let specialPrice = item.specialPrice;
                let location_id = 0;
                let description = item.description != undefined && item.description != null ? item.description : '';
                let inputs = [fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id, -1, inventory_quantity, image, specialPrice, cron_name, cron_id, location_id
                    , description
                ];
                //console.log('product---   ',inputs);
                await common.addCreateProduct(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });

                inputs = [
                    fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id,
                    -1, inventory_quantity, '', price, cron_name, cron_id, location_id
                    , description
                ];
                await common.addCreateProductVariant(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        //console.log(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
                let insImg = [image, item.sku, item.sku, fby_id, null, imageOrder];
                //console.log('images---   ',insImg);
                await common.addImages(insImg, fby_id, cron_name, cron_id, function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(insImg));
                    }
                });
                inputs = [fby_user_id, price, sku];
                await common.updatePrices(inputs, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductPricesFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
            });
        }

        return set_response;
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
};


exports.pushStoredenProduct = async (req, res) => {
    // for (const product of req.body.rows) {
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        common.userDetail(req.query.fby_user_id, cron_name, cron_id, function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                if (shopifyAccount.productPublish == 1) {

                                    try {
                                        //asynchronous function for updating shopify inventory
                                        try {
                                            createProductStoredenFromDigiConnector(req.body, shopifyAccount, fby_id, cron_name, cron_id)
                                                .then((params) => {
                                                    // if (Object.keys(params).length > 0) {
                                                    //   set_response[shopifyAccount.domain] = (params);
                                                    // }
                                                })
                                        } catch (error) {
                                            console.log(error.message);
                                        }
                                        await common.getCreateProductByDomain(shopifyAccount, "domain", cron_name, cron_id, async function (result) {
                                            if (result.error) {
                                                // store get product error log
                                                return false;
                                            } else {
                                                //asynchronous function for updating shopify inventory
                                                await storedenController.sendProductsStoreden(result.success.data, shopifyAccount, cron_name, cron_id)
                                                    .then((params) => {
                                                        // if (Object.keys(params).length > 0) {
                                                        //   set_response[shopifyAccount.domain] = (params);
                                                        // }
                                                    })

                                            }
                                        })
                                    } catch (error) {
                                        console.log(error.message);
                                    }

                                }
                                else {
                                    //console.log('\n shopifyController.js--> createProductsShopify--> PRODUCT_PUBLISH_MSG: ', constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG);
                                    set_response[shopifyAccount.domain] = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                }

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 15000);
                        }
                    })
                }
            }
        });

    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}



const createProductStoredenFromDigiConnector = async (product, shopifyAccount, fby_id, cron_name, cron_id, callback) => {
    try {

        let set_response = {};
        let page = 1;
        let total_page = 0;
        let count = 1;

        let ownerCode = shopifyAccount.owner_code;
        let groupCode = shopifyAccount.group_code;
        let item_per_page = constants.FBY_PERPAGE;
        let items = [];

        if (product !== undefined && product.length > 0) {
            product.forEach(async (item) => {
                let img = constants.DEFAULT_SHOPIFY_IMAGE;
                if (item.images_src != undefined && item.images_src != null) {
                    img = item.images_src;
                }
                let fby_user_id = fby_id;
                let domain = shopifyAccount.domain;
                let owner_code = ownerCode;
                let channel = groupCode;
                let sku = item.sku;
                let barcode = item.barcode;
                let item_id = 0;
                let title = item.name;
                let item_product_id = 0;
                let inventory_item_id = 0;
                let inventory_quantity = item.quantity;
                let image = item.images_src;//img;
                let imageOrder = item.image_position;

                let price = item.price;
                let specialPrice = item.specialPrice;
                let location_id = 0;
                let description = item.description != undefined && item.description != null ? item.description : '';
                let inputs = [fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id, -1, inventory_quantity, image, specialPrice, cron_name, cron_id, location_id
                    , description
                ];
                //console.log('product---   ',inputs);
                await common.addCreateProduct(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });

                inputs = [
                    fby_user_id, channel, domain, owner_code, sku, barcode, item_id, title, item_product_id, inventory_item_id,
                    -1, inventory_quantity, '', price, cron_name, cron_id, location_id
                    , description
                ];
                await common.addCreateProductVariant(inputs, fby_id, cron_name, cron_id, async function (result) {
                    if (result.error) {
                        //console.log(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
                let insImg = [image, item.sku, item.sku, fby_id, null, imageOrder];
                //console.log('images---   ',insImg);
                await common.addImages(insImg, fby_id, cron_name, cron_id, function (result) {
                    if (result.error) {
                        logger.logError(`getProductsFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(insImg));
                    }
                });
                inputs = [fby_user_id, price, sku];
                await common.updatePrices(inputs, async function (result) {
                    if (result.error) {
                        logger.logError(`getProductPricesFby fby_user_id: ${fby_id}, sku${sku}, ${url}`, JSON.stringify(inputs));
                    }
                });
            });
        }

        return set_response;
    }
    catch (error) {
        console.log(error.message);
        //console.log(`\nERROR ${constants.CC_OPERATIONS.GET_PRODUCT_FROM_FBY}: \n`, error.message);

    }
};




exports.deleteStoredenProduct = async (req, res) => {
    let file_and_method_name = 'shopifyController.js createProductsShopify';
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    try {

        common.userDetail(req.query.fby_user_id, cron_name, cron_id, function (result) {
            if (result.error) {
                // store user error log
                //send response
                if (!res.headersSent) {
                    res.send(result.error);
                }
            } else {
                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    //get shopify account detail
                    common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {

                            //send response
                            if (!res.headersSent) {
                                res.send(result.error);
                            }
                        } else {
                            let set_response = {};
                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                /**for each shopifyAccount
                                 * get product details from product table having same 'FBY_id' and 'domain' which were get from shopify channel table
                                */
                                await common.getCreatedProductIDBySku(req.body.sku, async function (product) {
                                    if (product.error) {

                                    } else {
                                        await deleteStoredenProduct(product.success.data[0], shopifyAccount, cron_name, cron_id)
                                            .then((params) => {
                                                // if (Object.keys(params).length > 0) {
                                                //   set_response[shopifyAccount.domain] = (params);
                                                // }
                                                console.log();
                                            })
                                    }
                                })

                            }
                            /* Shopify account loop end */
                            /**
                            * set time out is required to await to get all the responses from 'pushProductsShopify'
                            */
                            setTimeout(() => {
                                if (!res.headersSent) {
                                    res.send(set_response);
                                }
                            }, 15000);
                        }
                    })
                }
            }
        });

    }
    catch (error) {
        //console.log('\n ERROR: ', JSON.stringify(error.message));
    }
}

const deleteStoredenProduct = async (product, client) => {
    const url = `${client.domain}/products/product.json`;
    const options = {
        method: "delete",
        uri: url,
        headers: {
            "Content-Type": "application/json",
            'X-Shopify-Access-Token': `${client.token}`
        },
        body:
            { "uid": product.item_product_id },
        json: true,
    };
    await request(options)
        .then(async (parsedBody) => {
            common.deleteProducts(
                product.sku,
                client.fby_user_id,
                function (result) {
                    //do nothing
                    logger.logError(`Product Deleted`, JSON.stringify(result));
                });

            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nResult: \n${JSON.stringify(parsedBody)}`);
        })
        .catch((err) => {
            //console.log(`\n${logMsg}\n${JSON.stringify(options)}\nERROR: ${err.message}`);
        });
}

exports.assignBinNumber = async (req, res) => {
    let fby_user_id = req.query.fby_user_id;
    const { barcode, bin_number, order_no, order_line_item_id, status } = req.body;

    const input = [fby_user_id, barcode, bin_number, order_no, order_line_item_id, status];
    await common.assignBin(input, function (result) {
        if (result.error) {
            logger.logError(`assignBinNumber fby_user_id: ${fby_user_id}`, JSON.stringify(input));
        }
    });
    res.status(200).json({ success: true, message: 'Bin assigned successfully' });

}

exports.getBinDetails = async (req, res) => {
    let fby_user_id = req.query.fby_user_id;
    let order_no = req.query.order_no;

    const input = [fby_user_id, order_no];
    await common.getBin(input, function (result) {
        if (result.error) {
            res.send(result.error);
        } else {

            if (!res.headersSent) {
                res.send(result);
            }
        }
    });

}

exports.markOrderComplete = async (req, res) => {
    const { order_no, status } = req.body;
    let fby_user_id = req.query.fby_user_id;

    const input = [fby_user_id, order_no, status];
    await common.markOrderComplete(input, function (result) {
        if (result.error) {
            logger.logError(`assignBinNumber fby_user_id: ${fby_user_id}`, JSON.stringify(input));
        }
    });
    res.status(200).json({ success: true, message: 'Order marked as complete' });

}

exports.createShopifyOrder = async (req, res) => {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    let cron_id = "";
    let fby_user_id = req.query.fby_user_id;
    let cron_name = "";

    // Log API start and request details
    console.log(`[${new Date().toISOString()}] [${requestId}] ===== createShopifyOrder API STARTED =====`);
    console.log(`[${new Date().toISOString()}] [${requestId}] Request Details:`, {
        method: req.method,
        url: req.url,
        fby_user_id: fby_user_id,
        bodyKeys: Object.keys(req.body || {}),
        hasLineItems: !!(req.body?.line_items?.length),
        hasCustomer: !!(req.body?.customer),
        headers: {
            'content-type': req.headers['content-type'],
            'authorization': req.headers.authorization ? 'Bearer [REDACTED]' : 'Not provided'
        }
    });

    // Input validation
    if (!fby_user_id) {
        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Missing fby_user_id parameter`);
        return res.status(400).json({
            error: 'fby_user_id is required',
            requestId: requestId
        });
    }

    if (!req.body) {
        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Missing request body`);
        return res.status(400).json({
            error: 'Request body is required',
            requestId: requestId
        });
    }

    // Validate required order fields
    const requiredFields = ['line_items', 'customer'];
    const missingFields = requiredFields.filter(field => !req.body[field]);

    if (missingFields.length > 0) {
        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Missing required fields:`, missingFields);
        return res.status(400).json({
            error: `Missing required fields: ${missingFields.join(', ')}`,
            requestId: requestId
        });
    }

    if (!Array.isArray(req.body.line_items) || req.body.line_items.length === 0) {
        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: line_items must be a non-empty array`);
        return res.status(400).json({
            error: 'line_items must be a non-empty array',
            requestId: requestId
        });
    }

    // Validate line items
    for (let i = 0; i < req.body.line_items.length; i++) {
        const item = req.body.line_items[i];
        if (!item.variant_id || !item.quantity) {
            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Invalid line item at index ${i}:`, item);
            return res.status(400).json({
                error: `Line item at index ${i} must have variant_id and quantity`,
                requestId: requestId
            });
        }
    }

    console.log(`[${new Date().toISOString()}] [${requestId}] Input validation passed`);

    try {
        console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.userDetail for fby_user_id: ${fby_user_id}`);

        await common.userDetail(req.query.fby_user_id, cron_name, cron_id, async function (result) {
            if (result.error) {
                console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in userDetail:`, result.error);
                if (!res.headersSent) {
                    return res.status(404).json({
                        error: 'User not found or invalid',
                        details: result.error,
                        requestId: requestId
                    });
                }
            } else {
                console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: userDetail returned ${result.success.data.length} clients`);

                for (const client of result.success.data) {
                    let fby_id = client.fby_user_id;
                    console.log(`[${new Date().toISOString()}] [${requestId}] Processing client with fby_id: ${fby_id}`);

                    console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: common.shopifyUserDetail for fby_id: ${fby_id}`);
                    await common.shopifyUserDetail(fby_id, cron_name, cron_id, async function (result) {
                        if (result.error) {
                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in shopifyUserDetail:`, result.error);
                            if (!res.headersSent) {
                                return res.status(404).json({
                                    error: 'Shopify account not found or invalid',
                                    details: result.error,
                                    requestId: requestId
                                });
                            }
                        } else {
                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: shopifyUserDetail returned ${result.success.data.length} shopify accounts`);

                            let orderCreated = false;
                            let errorResponse = null;

                            /* Shopify account loop start */
                            for (const shopifyAccount of result.success.data) {
                                console.log(`[${new Date().toISOString()}] [${requestId}] Processing shopify account:`, {
                                    domain: shopifyAccount.domain,
                                    productPublish: shopifyAccount.productPublish,
                                    owner_code: shopifyAccount.owner_code,
                                    group_code: shopifyAccount.group_code
                                });

                                if (shopifyAccount.productPublish == 1) {
                                    try {
                                        console.log(`[${new Date().toISOString()}] [${requestId}] CALLING: shopifyController.createShopifyOrder`);
                                        const createOrderResult = await shopifyController.createShopifyOrder(fby_user_id, req.body, requestId);

                                        if (createOrderResult && createOrderResult.success) {
                                            console.log(`[${new Date().toISOString()}] [${requestId}] SUCCESS: Order created successfully`);
                                            orderCreated = true;

                                            if (!res.headersSent) {
                                                return res.status(201).json({
                                                    success: true,
                                                    message: 'Order created successfully',
                                                    order: createOrderResult.order,
                                                    domain: shopifyAccount.domain,
                                                    requestId: requestId
                                                });
                                            }
                                        } else {
                                            console.log(`[${new Date().toISOString()}] [${requestId}] ERROR: Order creation failed:`, createOrderResult?.error || 'Unknown error');
                                            errorResponse = createOrderResult?.error || 'Order creation failed';
                                        }
                                    } catch (error) {
                                        console.log(`[${new Date().toISOString()}] [${requestId}] ERROR in shopifyController.createShopifyOrder:`, {
                                            message: error.message,
                                            stack: error.stack,
                                            domain: shopifyAccount.domain
                                        });
                                        errorResponse = error.message;
                                    }
                                } else {
                                    console.log(`[${new Date().toISOString()}] [${requestId}] Product publishing is DISABLED for domain: ${shopifyAccount.domain}`);
                                    errorResponse = constants.CC_PRODUCT_PUBLISH_UPDATE_NOT_ALLOWED_MSG;
                                }
                            }
                            /* Shopify account loop end */

                            // If we reach here and no order was created, send error response
                            if (!orderCreated && !res.headersSent) {
                                console.log(`[${new Date().toISOString()}] [${requestId}] FINAL ERROR: No order was created`);
                                return res.status(500).json({
                                    success: false,
                                    error: errorResponse || 'Failed to create order',
                                    requestId: requestId
                                });
                            }
                        }
                    });
                }
            }
        });

        res.on('finish', function () {
            console.log(`[${new Date().toISOString()}] [${requestId}] Response finished event triggered`);
        });
    }
    catch (error) {
        console.log(`[${new Date().toISOString()}] [${requestId}] FATAL ERROR in createShopifyOrder:`, {
            message: error.message,
            stack: error.stack,
            fby_user_id: fby_user_id
        });

        if (!res.headersSent) {
            return res.status(500).json({
                success: false,
                error: 'Internal server error',
                message: error.message,
                requestId: requestId
            });
        }
    }
}
exports.updateShopifyInventory = updateShopifyInventory;