const request = require('supertest');
const chai = require('chai');
const expect = chai.expect;

// Replace with your app's base URL if necessary
const app = require('../app'); // Assuming the Express app is exported from 'app.js'

describe('POST /common/api/create_shopify_order', () => {
    it('should create a Shopify order and store it in database successfully', async () => {
        // Sample order data for testing
        const orderData = {
            "line_items": [
                {
                    "title": "Test Product",
                    "price": "29.99",
                    "quantity": 2,
                    "sku": "TEST-SKU-001",
                    "variant_id": *********,
                    "product_id": *********,
                    "variant_title": "Default Title",
                    "vendor": "Test Vendor",
                    "fulfillment_service": "manual",
                    "requires_shipping": true,
                    "taxable": true,
                    "gift_card": false
                }
            ],
            "customer": {
                "first_name": "<PERSON>",
                "last_name": "<PERSON><PERSON>",
                "email": "<EMAIL>",
                "phone": "+*********0"
            },
            "shipping_address": {
                "first_name": "<PERSON>",
                "last_name": "<PERSON><PERSON>",
                "address1": "123 Test Street",
                "address2": "Apt 4B",
                "city": "Test City",
                "province": "Test State",
                "province_code": "TS",
                "zip": "12345",
                "country": "United States",
                "country_code": "US",
                "phone": "+*********0"
            },
            "billing_address": {
                "first_name": "John",
                "last_name": "Doe",
                "address1": "123 Test Street",
                "address2": "Apt 4B",
                "city": "Test City",
                "province": "Test State",
                "province_code": "TS",
                "zip": "12345",
                "country": "United States",
                "country_code": "US",
                "phone": "+*********0"
            },
            "email": "<EMAIL>",
            "currency": "USD",
            "financial_status": "pending",
            "fulfillment_status": null,
            "total_price": "59.98",
            "total_line_items_price": "59.98",
            "total_tax": "0.00",
            "total_discounts": "0.00",
            "total_shipping_price_set": {
                "shop_money": {
                    "amount": "0.00",
                    "currency_code": "USD"
                }
            },
            "gateway": "shopify_payments",
            "transactions": [
                {
                    "kind": "sale",
                    "gateway": "shopify_payments",
                    "status": "pending",
                    "amount": "59.98",
                    "currency": "USD"
                }
            ]
        };

        // Use a valid fby_user_id for testing - replace with actual test user ID
        const fby_user_id = 1; // Replace with a valid test user ID
        
        const response = await request(app)
            .post('/common/api/create_shopify_order')
            .query({ fby_user_id: fby_user_id })
            .send(orderData)
            .set('Accept', 'application/json')
            .set('Content-Type', 'application/json');

        // Log the response for debugging
        console.log('Response Status:', response.status);
        console.log('Response Body:', JSON.stringify(response.body, null, 2));

        // Assert the response
        if (response.status === 201) {
            // Success case
            expect(response.body).to.have.property('success', true);
            expect(response.body).to.have.property('message', 'Order created successfully');
            expect(response.body).to.have.property('order');
            expect(response.body.order).to.have.property('id');
            expect(response.body.order).to.have.property('order_number');
            console.log('✅ Order created successfully:', response.body.order.id);
        } else if (response.status === 404) {
            // No Shopify account found
            expect(response.body).to.have.property('error');
            console.log('⚠️ No Shopify account found for user:', fby_user_id);
        } else if (response.status === 500) {
            // Server error
            expect(response.body).to.have.property('error');
            console.log('❌ Server error:', response.body.error);
        } else {
            // Other error cases
            console.log('❌ Unexpected response:', response.status, response.body);
        }

        // The test should not fail if there's no valid Shopify account configured
        // This is more of an integration test that requires proper setup
        expect(response.status).to.be.oneOf([201, 404, 500]);
    });

    it('should return error for missing fby_user_id', async () => {
        const orderData = {
            "line_items": [
                {
                    "title": "Test Product",
                    "price": "29.99",
                    "quantity": 1,
                    "sku": "TEST-SKU-001"
                }
            ]
        };

        const response = await request(app)
            .post('/common/api/create_shopify_order')
            .send(orderData)
            .set('Accept', 'application/json')
            .set('Content-Type', 'application/json');

        // Should return error for missing fby_user_id
        expect(response.status).to.be.oneOf([400, 404, 500]);
        expect(response.body).to.have.property('error');
    });
});
